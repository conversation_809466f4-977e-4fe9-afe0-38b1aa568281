import { Card, CardBody } from '@heroui/react';

/**
 * ExploreRecommendationSkeleton Component
 * 
 * Dedicated skeleton loader for the ExploreRecommendation page that exactly matches:
 * - Large image (230px width) with 4 small images grid below
 * - Detailed text content (title, duration, location, features, tags, description)
 * - Right section with rating, pricing, and action button
 * - Header with "Recommendation" title and "Filter" button
 */

const ExploreRecommendationCardSkeleton = () => {
  return (
    <div className="flex flex-col gap-2 flex-1 overflow-hidden mb-3">
      <Card className="bg-white border-none shadow-none hover:shadow-lg transition-all duration-300 ease-in-out flex-shrink-0">
        <CardBody>
          <div className="flex flex-row max-md:flex-col justify-between rounded-xl cursor-pointer w-full">
            {/* Left section: Image grid and details skeleton */}
            <div className="flex flex-row max-md:flex-col gap-4">
              {/* Image Grid Skeleton - matches ExploreRecommendation layout */}
              <div className="flex flex-col gap-1.5 md:w-[230px]">
                {/* Large Image Skeleton */}
                <div className="w-full">
                  <div className="w-full h-[120px] bg-gray-200 rounded-tl-xl rounded-tr-xl animate-pulse" />
                </div>

                {/* Small Image Grid Skeleton - 4 images in a row */}
                <div className="grid grid-cols-4 gap-1.5">
                  <div className="w-full aspect-square bg-gray-200 rounded-bl-md animate-pulse" />
                  <div className="w-full aspect-square bg-gray-200 animate-pulse" />
                  <div className="w-full aspect-square bg-gray-200 animate-pulse" />
                  <div className="relative">
                    <div className="w-full aspect-square bg-gray-200 rounded-br-md animate-pulse" />
                    {/* "View All" overlay skeleton */}
                    <div className="absolute inset-0 flex items-center justify-center">
                      <div className="w-12 h-4 bg-gray-300 rounded animate-pulse" />
                    </div>
                  </div>
                </div>
              </div>

              {/* Text content skeleton - matches ExploreRecommendation text layout */}
              <div className="text-sm space-y-0.5 flex-1">
                {/* Title skeleton */}
                <div className="w-40 h-6 bg-gray-200 rounded animate-pulse mb-2" />
                
                {/* Duration and location row skeleton */}
                <div className="flex flex-row gap-3 py-2">
                  <div className="flex flex-row gap-1 items-center">
                    <div className="w-4 h-4 bg-gray-200 rounded animate-pulse" />
                    <div className="w-24 h-4 bg-gray-200 rounded animate-pulse" />
                  </div>
                  <div className="flex flex-row gap-1 items-center">
                    <div className="w-4 h-4 bg-gray-200 rounded animate-pulse" />
                    <div className="w-16 h-4 bg-gray-200 rounded animate-pulse" />
                  </div>
                </div>

                {/* Features skeleton - "Book with only 30%" and "Customize free" */}
                <div className="space-y-1">
                  <div className="w-48 h-4 bg-gray-200 rounded animate-pulse" />
                  <div className="w-36 h-4 bg-gray-200 rounded animate-pulse" />
                </div>

                {/* Tags skeleton - "Activities, Explore, Leisure, Family" */}
                <div className="py-2">
                  <div className="w-56 h-4 bg-gray-200 rounded animate-pulse" />
                </div>

                {/* Description skeleton - 2 lines */}
                <div className="space-y-1">
                  <div className="w-full h-4 bg-gray-200 rounded animate-pulse" />
                  <div className="w-3/4 h-4 bg-gray-200 rounded animate-pulse" />
                </div>
              </div>
            </div>

            {/* Right section: Rating, pricing, and action button skeleton */}
            <div className="flex flex-col gap-2 justify-between text-right min-w-[150px]">
              {/* Rating skeleton - "4.2 (103 Ratings)" */}
              <div className="w-32 h-5 bg-gray-200 rounded animate-pulse ml-auto" />
              
              <div className="text-right space-y-2">
                {/* Price skeleton - "$3,500" and "$2,999" */}
                <div className="flex justify-end items-center gap-3">
                  <div className="w-12 h-4 bg-gray-200 rounded animate-pulse" />
                  <div className="w-16 h-6 bg-gray-200 rounded animate-pulse" />
                </div>
                
                {/* Taxes skeleton - "$150 taxes & fees Per Person" */}
                <div className="flex justify-end py-2">
                  <div className="w-[130px] h-8 bg-gray-200 rounded animate-pulse" />
                </div>
                
                {/* "Book Trip" button skeleton */}
                <div className="w-20 h-8 bg-gray-200 rounded-full animate-pulse ml-auto" />
              </div>
            </div>
          </div>
        </CardBody>
      </Card>
    </div>
  );
};

/**
 * Complete ExploreRecommendation page skeleton with header and multiple cards
 */
const ExploreRecommendationSkeleton = ({ count = 5 }: { count?: number }) => {
  return (
    <div className="relative">
      {/* Header skeleton - matches ExploreRecommendation header */}
      <div className="flex flex-row items-center justify-between flex-shrink-0 md:sticky md:top-0 md:z-50 mb-2 bg-[#F2F2FF]">
        <div>
          {/* "Recommendation" title skeleton */}
          <div className="w-32 h-6 bg-gray-200 rounded animate-pulse" />
        </div>
        <div className="flex flex-row items-center gap-2">
          {/* Sort icon skeleton */}
          <div className="w-4 h-4 bg-gray-200 rounded animate-pulse" />
          {/* "Filter" text skeleton */}
          <div className="w-12 h-4 bg-gray-200 rounded animate-pulse" />
        </div>
      </div>

      {/* Multiple recommendation card skeletons */}
      {Array.from({ length: count }).map((_, index) => (
        <ExploreRecommendationCardSkeleton key={`explore-rec-skeleton-${index}`} />
      ))}
    </div>
  );
};

export default ExploreRecommendationSkeleton;
export { ExploreRecommendationCardSkeleton };
