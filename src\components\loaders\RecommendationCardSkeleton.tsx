import { Card, CardBody } from '@heroui/react';

/**
 * RecommendationCardSkeleton Component
 *
 * Skeleton loader that exactly matches the RecommendationCard layout:
 * - Large image with 4 small images grid
 * - Text content (title, duration, location, features, tags, description)
 * - Right section with rating, price, and action button
 * - Responsive design matching the original card
 */

const RecommendationCardSkeleton = () => {
  return (
    <div className="flex flex-col gap-2 flex-1 overflow-hidden mb-3">
      <Card className="bg-white border-none shadow-none">
        <CardBody>
          <div className="flex flex-row max-md:flex-col justify-between rounded-xl w-full">
            {/* Left section: Image and details skeleton */}
            <div className="flex flex-row max-md:flex-col gap-4">
              {/* Image Grid Skeleton */}
              <div className="flex flex-col gap-1.5 md:w-[230px]">
                {/* Large Image Skeleton */}
                <div className="w-full">
                  <div className="w-full h-[120px] bg-gray-200 rounded-tl-xl rounded-tr-xl animate-pulse" />
                </div>

                {/* Small Image Grid Skeleton */}
                <div className="grid grid-cols-4 gap-1.5">
                  <div className="w-full aspect-square bg-gray-200 rounded-bl-md animate-pulse" />
                  <div className="w-full aspect-square bg-gray-200 animate-pulse" />
                  <div className="w-full aspect-square bg-gray-200 animate-pulse" />
                  <div className="relative">
                    <div className="w-full aspect-square bg-gray-200 rounded-br-md animate-pulse" />
                    <div className="absolute inset-0 flex items-center justify-center">
                      <div className="w-12 h-4 bg-gray-300 rounded animate-pulse" />
                    </div>
                  </div>
                </div>
              </div>

              {/* Text content skeleton */}
              <div className="text-sm space-y-0.5 flex-1">
                {/* Title skeleton */}
                <div className="w-40 h-6 bg-gray-200 rounded animate-pulse mb-2" />

                {/* Duration and location skeleton */}
                <div className="flex flex-row gap-3 py-2">
                  <div className="flex flex-row gap-1 items-center">
                    <div className="w-4 h-4 bg-gray-200 rounded animate-pulse" />
                    <div className="w-24 h-4 bg-gray-200 rounded animate-pulse" />
                  </div>
                  <div className="flex flex-row gap-1 items-center">
                    <div className="w-4 h-4 bg-gray-200 rounded animate-pulse" />
                    <div className="w-16 h-4 bg-gray-200 rounded animate-pulse" />
                  </div>
                </div>

                {/* Features skeleton */}
                <div className="space-y-1">
                  <div className="w-48 h-4 bg-gray-200 rounded animate-pulse" />
                  <div className="w-36 h-4 bg-gray-200 rounded animate-pulse" />
                </div>

                {/* Tags skeleton */}
                <div className="py-2">
                  <div className="w-56 h-4 bg-gray-200 rounded animate-pulse" />
                </div>

                {/* Description skeleton */}
                <div className="space-y-1">
                  <div className="w-full h-4 bg-gray-200 rounded animate-pulse" />
                  <div className="w-3/4 h-4 bg-gray-200 rounded animate-pulse" />
                </div>
              </div>
            </div>

            {/* Right section: Action button skeleton */}
            <div className="flex flex-col gap-2 justify-between text-right min-w-[150px]">
              {/* Rating skeleton */}
              <div className="w-32 h-5 bg-gray-200 rounded animate-pulse ml-auto" />

              <div className="text-right space-y-2">
                {/* Price skeleton */}
                <div className="flex justify-end items-center gap-3">
                  <div className="w-12 h-4 bg-gray-200 rounded animate-pulse" />
                  <div className="w-16 h-6 bg-gray-200 rounded animate-pulse" />
                </div>

                {/* Taxes skeleton */}
                <div className="flex justify-end py-2">
                  <div className="w-[130px] h-8 bg-gray-200 rounded animate-pulse" />
                </div>

                {/* Button skeleton */}
                <div className="w-20 h-8 bg-gray-200 rounded-full animate-pulse ml-auto" />
              </div>
            </div>
          </div>
        </CardBody>
      </Card>
    </div>
  );
};

// Component to show multiple skeleton cards with header
const RecommendationListSkeleton = ({ count = 5 }: { count?: number }) => {
  return (
    <div className="relative">
      {/* Header skeleton */}
      <div className="flex flex-row items-center justify-between flex-shrink-0 md:sticky md:top-0 md:z-50 mb-2 bg-[#F2F2FF]">
        <div>
          <div className="w-32 h-6 bg-gray-200 rounded animate-pulse" />
        </div>
        <div className="flex flex-row items-center gap-2">
          <div className="w-4 h-4 bg-gray-200 rounded animate-pulse" />
          <div className="w-12 h-4 bg-gray-200 rounded animate-pulse" />
        </div>
      </div>

      {/* Skeleton cards */}
      {Array.from({ length: count }).map((_, index) => (
        <RecommendationCardSkeleton key={index} />
      ))}
    </div>
  );
};

export default RecommendationCardSkeleton;
export { RecommendationListSkeleton };
