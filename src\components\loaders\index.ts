/**
 * Skeleton Loaders Export Index
 *
 * This file exports all skeleton loader components for easy importing.
 * Each skeleton loader matches the design and layout of its corresponding component.
 *
 * Usage Examples:
 *
 * // Import individual skeleton loaders
 * import { HeaderSkeleton, SidebarSkeleton } from '@/components/loaders';
 *
 * // Import the complete main page skeleton
 * import { MainPageSkeleton } from '@/components/loaders';
 *
 * // Import specific section skeletons
 * import { LandingPageSkeleton, RecommendationSkeleton } from '@/components/loaders';
 */

// Individual Component Skeletons
export { default as HeaderSkeleton } from './HeaderSkeleton';
export { default as SidebarSkeleton } from './SidebarSkeleton';
export { default as PrimaryFilterSkeleton } from './PrimaryFilterSkeleton';
export { default as RecommendationCardSkeleton } from './RecommendationCardSkeleton';
export { default as RecommendationSkeleton } from './RecommendationSkeleton';
export { default as ExploreRecommendationSkeleton, ExploreRecommendationCardSkeleton } from './ExploreRecommendationSkeleton';
export { default as ChatSkeleton } from './ChatSkeleton';

// Layout Skeletons
export { default as AdminLayoutSkeleton } from './AdminLayoutSkeleton';
export { default as LandingPageSkeleton } from './LandingPageSkeleton';

// Complete Page Skeleton
export { default as MainPageSkeleton } from './MainPageSkeleton';
