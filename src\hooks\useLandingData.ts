import { useFetch } from './useAPiCall';

// Mock API functions - replace these with your actual API calls
const fetchFilterData = async () => {
  // Replace with actual API endpoint
  // const response = await fetch('/api/filter-data');
  // return response.json();
  
  // For now, return mock data immediately
  return Promise.resolve({ filters: [] });
};

const fetchChatData = async () => {
  // Replace with actual API endpoint
  // const response = await fetch('/api/chat-data');
  // return response.json();
  
  // For now, return mock data immediately
  return Promise.resolve({ messages: [] });
};

const fetchRecommendations = async () => {
  // Replace with actual API endpoint
  // const response = await fetch('/api/recommendations');
  // return response.json();
  
  // For now, return mock data immediately
  return Promise.resolve({ recommendations: [] });
};

export const useLandingData = () => {
  const filterQuery = useFetch(['filter-data'], fetchFilterData);
  const chatQuery = useFetch(['chat-data'], fetchChatData);
  const recommendationsQuery = useFetch(['recommendations-data'], fetchRecommendations);

  return {
    filter: {
      data: filterQuery.data,
      isLoading: filterQuery.isLoading,
      error: filterQuery.error,
    },
    chat: {
      data: chatQuery.data,
      isLoading: chatQuery.isLoading,
      error: chatQuery.error,
    },
    recommendations: {
      data: recommendationsQuery.data,
      isLoading: recommendationsQuery.isLoading,
      error: recommendationsQuery.error,
    },
    isAnyLoading: filterQuery.isLoading || chatQuery.isLoading || recommendationsQuery.isLoading,
  };
};
