import { useState, useEffect } from 'react';

import PrimaryFilter from '@/components/globalComponents/primaryFilter';
import {
  PrimaryFilterSkeleton,
  RecommendationSkeleton,
  ChatSkeleton,
} from '@/components/loaders';

import Recommendation from './Recommendation';
import ChatWithShasa from '@/components/globalComponents/ChatWithShasa';

interface LandingPageProps {
  isLoading?: boolean;
  loadingComponents?: {
    filter?: boolean;
    chat?: boolean;
    recommendations?: boolean;
  };
}

const LandingPage = ({
  isLoading = false,
  loadingComponents = {},
}: LandingPageProps) => {
  const [componentLoading, setComponentLoading] = useState({
    filter: loadingComponents.filter || isLoading,
    chat: loadingComponents.chat || isLoading,
    recommendations: loadingComponents.recommendations || isLoading,
  });

  // console.log(isLogin);

  // Simulate staggered loading for better UX
  useEffect(() => {
    if (isLoading) {
      // Filter loads first
      setTimeout(() => {
        setComponentLoading(prev => ({ ...prev, filter: false }));
      }, 800);

      // Chat loads second
      setTimeout(() => {
        setComponentLoading(prev => ({ ...prev, chat: false }));
      }, 1200);

      // Recommendations load last
      setTimeout(() => {
        setComponentLoading(prev => ({ ...prev, recommendations: false }));
      }, 1600);
    }
  }, [isLoading]);
  // if (!isLogin) {
  //   console.log('not login');
  // }

  return (
    <div className="p-3 sm:p-5 rounded-tl-xl h-[calc(100vh-73px)] flex flex-col">
      <div className="">
        <p className="text-sm sm:text-base text-default-1000 opacity-75">
          Welcome to NxVoy!
        </p>
        <div className="text-2xl sm:text-4xl font-semibold text-subtitle">
          <span className="text-gradient">Meet Shasa</span>, Your Travel
          Companion!
        </div>
      </div>

      {/* Primary Filter Section */}
      <div className="py-3 sm:py-4">
        {componentLoading.filter ? (
          <PrimaryFilterSkeleton />
        ) : (
          <PrimaryFilter />
        )}
      </div>

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-5 gap-3 sm:gap-4 flex-1 min-h-0">
        {/* Chat Section */}
        <div className="lg:col-span-3 order-2 lg:order-1">
          {componentLoading.chat ? <ChatSkeleton /> : <ChatWithShasa />}
        </div>

        {/* Recommendations Section */}
        <div className="lg:col-span-2 order-1 lg:order-2">
          {componentLoading.recommendations ? (
            <RecommendationSkeleton cardCount={3} />
          ) : (
            <Recommendation />
          )}
        </div>
      </div>
    </div>
  );
};

export default LandingPage;
