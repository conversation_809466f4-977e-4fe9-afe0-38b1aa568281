'use client';

import React, { useState, useRef, useEffect } from 'react';
import { Plus } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import Image from 'next/image';
import { Button } from '@heroui/react';

import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Input } from '@/components/ui/input';
import { MicrophoneIcon, StarFallIcon } from '@/components/icons';

interface Message {
  id: string;
  type: 'user' | 'assistant';
  content: string;
  timestamp: string;
}

const ChatWithShasa = () => {
  const [messages, setMessages] = useState<Message[]>([
    {
      id: '1',
      type: 'assistant',
      content:
        "Hello! Where do you dream of traveling? I'm here to help you create the perfect holiday itinerary.",
      timestamp: '11:36 AM',
    },
    {
      id: '2',
      type: 'assistant',
      content: "Let's get started!",
      timestamp: '11:36 AM',
    },
    {
      id: '3',
      type: 'user',
      content:
        'Book a flight from London to Germany on 28th August 2025, for 1 adult and economy travel type.',
      timestamp: '11:40 AM',
    },
    {
      id: '4',
      type: 'assistant',
      content:
        'To help you book a flight from London to Germany on 28th August 2025 for 1 adult in economy class, could you please confirm the following details:',
      timestamp: '11:40 AM',
    },

    {
      id: '1',
      type: 'assistant',
      content:
        "Hello! Where do you dream of traveling? I'm here to help you create the perfect holiday itinerary.",
      timestamp: '11:36 AM',
    },
    {
      id: '2',
      type: 'assistant',
      content: "Let's get started!",
      timestamp: '11:36 AM',
    },
    {
      id: '3',
      type: 'user',
      content:
        'Book a flight from London to Germany on 28th August 2025, for 1 adult and economy travel type.',
      timestamp: '11:40 AM',
    },
    {
      id: '4',
      type: 'assistant',
      content:
        'To help you book a flight from London to Germany on 28th August 2025 for 1 adult in economy class, could you please confirm the following details:',
      timestamp: '11:40 AM',
    },
  ]);

  const [inputValue, setInputValue] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const chatContainerRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const handleSendMessage = async () => {
    if (!inputValue.trim()) return;

    const newMessage: Message = {
      id: Date.now().toString(),
      type: 'user',
      content: inputValue,
      timestamp: new Date().toLocaleTimeString([], {
        hour: '2-digit',
        minute: '2-digit',
      }),
    };

    setMessages(prev => [...prev, newMessage]);
    setInputValue('');
    setIsTyping(true);

    try {
      // Replace with actual API call to your chat service
      // const response = await fetch('/api/chat', {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify({ message: inputValue })
      // });
      // const data = await response.json();

      // For now, provide immediate response (remove this when you have real API)
      const assistantMessage: Message = {
        id: (Date.now() + 1).toString(),
        type: 'assistant',
        content:
          "I'll help you with that request. Let me process the information and provide you with the best options.",
        timestamp: new Date().toLocaleTimeString([], {
          hour: '2-digit',
          minute: '2-digit',
        }),
      };
      setMessages(prev => [...prev, assistantMessage]);
    } catch (error) {
      console.error('Error sending message:', error);
      // Handle error state
    } finally {
      setIsTyping(false);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const quickActions = [
    'Winter',
    'Explore local cuisines',
    'Visit historical landmarks',
    'Discover hidden gems',
    'Experience nightlife',
  ];

  return (
    <div className="w-full h-[calc(100vh-275px)] bg-gradient-to-br from-[#C084FC] via-[#8B5CF6] to-[#17074C] rounded-xl p-4 sm:p-6 flex flex-col relative overflow-hidden">
      {/* Background blur effects */}
      <div className="absolute inset-0 bg-gradient-to-br from-white/10 via-transparent to-black/20 rounded-xl" />
      <div className="absolute top-20 right-20 w-32 h-32 bg-white/10 rounded-full blur-xl" />
      <div className="absolute bottom-32 left-16 w-24 h-24 bg-purple-300/20 rounded-full blur-lg" />

      {/* Header */}
      <div className="flex items-center justify-between mb-4 flex-shrink-0 relative z-10">
        <div className="flex items-center space-x-2">
          <Button
            color="secondary"
            startContent={
              <Image
                src="https://storage.googleapis.com/nxvoytrips-img/nxvoy-travel-agent-holidayPlanner/logo.svg"
                alt="logo"
                width={18}
                height={18}
                className="w-[18px] h-[18px] object-cover filter invert brightness-0"
              />
            }
            variant="flat"
            className="rounded-full text-white font-medium text-[14px] h-[31px]"
          >
            Chat with Shasa
          </Button>
        </div>
      </div>

      {/* Messages Container - Scrollable */}
      <div
        ref={chatContainerRef}
        className="flex-1 custom-blur hide-scrollbar rounded-lg p-3 overflow-y-auto space-y-4 mb-4 relative z-10"
        style={{ maxHeight: 'calc(100% - 150px)' }}
      >
        <AnimatePresence>
          {messages.map((message, index) => (
            <motion.div
              key={message.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.3, delay: index * 0.1 }}
              className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}
            >
              <div
                className={`flex items-start max-w-[85%] ${
                  message.type === 'user' ? 'flex-row-reverse ml-auto' : ''
                }`}
              >
                <div className="flex flex-col relative">
                  {/* Curved Bubble Tail */}
                  <svg
                    className={`absolute w-3 h-3 ${
                      message.type === 'user'
                        ? 'top-[4.1px] -right-[12px]'
                        : 'top-[4.1px] -left-[12px]'
                    }`}
                    viewBox="0 0 10 10"
                  >
                    <path
                      d={
                        message.type === 'user'
                          ? 'M0,0 Q10,3 0,10 Z' // tail pointing left
                          : 'M10,0 Q0,5 10,10 Z' // tail pointing right
                      }
                      fill={
                        message.type === 'user'
                          ? 'rgba(0, 0, 0, 0.3)'
                          : 'rgba(255,255,255,0.1)'
                      }
                    />
                  </svg>

                  {/* Message Bubble */}
                  <div
                    className={`px-4 py-3 mt-1 backdrop-blur-lg ${
                      message.type === 'user'
                        ? 'bg-black/30 text-white ml-auto rounded-lg rounded-tr-none'
                        : 'bg-white/10 text-white rounded-lg rounded-tl-none'
                    }`}
                  >
                    {message.type === 'assistant' && (
                      <div className="flex items-center space-x-2 mb-1">
                        <span className="text-white text-sm font-semibold">
                          Shasa
                        </span>
                        <span className="text-white/60 text-xs">
                          AI travel assistant
                        </span>
                      </div>
                    )}
                    <p className="text-sm leading-relaxed whitespace-pre-wrap">
                      {message.content}
                    </p>
                    <div className="flex justify-end mt-2">
                      <span className="text-xs text-white/60">
                        {message.timestamp}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>
          ))}
        </AnimatePresence>

        {isTyping && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="flex justify-start"
          >
            <div className="flex items-start space-x-2 max-w-[85%]">
              <Avatar className="w-8 h-8 flex-shrink-0">
                <AvatarFallback className="bg-white/20 text-white text-xs font-medium">
                  S
                </AvatarFallback>
              </Avatar>
              <div className="bg-white/10 rounded-2xl rounded-bl-md px-4 py-3 backdrop-blur-sm">
                <div className="flex space-x-1">
                  <div className="w-2 h-2 bg-white/60 rounded-full animate-bounce" />
                  <div
                    className="w-2 h-2 bg-white/60 rounded-full animate-bounce"
                    style={{ animationDelay: '0.1s' }}
                  />
                  <div
                    className="w-2 h-2 bg-white/60 rounded-full animate-bounce"
                    style={{ animationDelay: '0.2s' }}
                  />
                </div>
              </div>
            </div>
          </motion.div>
        )}

        <div ref={messagesEndRef} />
      </div>

      {/* Quick Action Buttons - Scrollable */}
      <div className="mb-3 relative z-10 ">
        <div className="flex space-x-2 overflow-x-auto hide-scrollbar pb-2">
          {quickActions.map(action => (
            <Button
              key={action}
              variant="bordered"
              size="sm"
              className="bg-transparent border-white/20 text-white hover:bg-white/5 hover:text-white whitespace-nowrap flex-shrink-0 backdrop-blur-sm rounded-full h-[29px] text-sm font-medium"
              onClick={() => setInputValue(action)}
            >
              {action}
            </Button>
          ))}
        </div>
      </div>

      {/* Input Area */}
      <div className="flex items-center space-x-3 flex-shrink-0 relative z-10">
        <div className="flex-1 relative">
          <Input
            value={inputValue}
            onChange={e => setInputValue(e.target.value)}
            onKeyDown={handleKeyDown}
            placeholder="Ask me anything travel related..."
            // className="bg-white/10 border-2 border-white/20 text-white placeholder:text-white/60 rounded-full pr-12 h-10 backdrop-blur-sm focus:bg-white/15 focus:border-white/30"
            className="bg-white outline-[2px] outline-white/20 text-black placeholder:text-[#AEAEB2] rounded-full pr-12 h-10 backdrop-blur-sm"
          />
        </div>

        <Button
          onPress={handleSendMessage}
          color="secondary"
          startContent={<StarFallIcon />}
          variant="bordered"
          className="rounded-full text-white font-medium text-[14px] h-10 border-white/20"
        >
          Generate travel
        </Button>

        <Button
          variant="bordered"
          className="bg-white outline-[2px] outline-white/20  text-[#1C274C] hover:text-[#1C274C] rounded-full flex-shrink-0 w-10 h-10 min-w-10 max-w-10 p-0 backdrop-blur-sm"
        >
          <Plus className="w-5 h-5" />
        </Button>
        <Button
          //   size="icon"
          variant="bordered"
          className="bg-white outline-[2px] outline-white/20 text-[#1C274C] hover:text-[#1C274C] rounded-full flex-shrink-0 w-12 h-10 min-w-10 max-w-10 p-0 backdrop-blur-sm"
        >
          <MicrophoneIcon />
        </Button>
      </div>
    </div>
  );
};

export default ChatWithShasa;
