import ChatSkeleton from '@/components/loaders/ChatSkeleton';
import PrimaryFilter from '@/components/globalComponents/primaryFilter';
import PrimaryFilterSkeleton from '@/components/loaders/PrimaryFilterSkeleton';
import { RecommendationListSkeleton } from '@/components/loaders';
import Image from 'next/image';
import ChatWithShasa from '@/components/globalComponents/ChatWithShasa';
import Recommendation from './Recommendation';

interface DiscoverPageProps {
  isLoading?: boolean;
  loadingComponents?: {
    filter?: boolean;
    chat?: boolean;
    recommendations?: boolean;
  };
}
const ExploreRecommendation = ({
  isLoading = false,
  loadingComponents = {},
}: DiscoverPageProps) => {
  // Use the loading states directly from props
  const componentLoading = {
    filter: loadingComponents.filter ?? isLoading,
    chat: loadingComponents.chat ?? isLoading,
    recommendations: loadingComponents.recommendations ?? isLoading,
  };
  return (
    <div className="p-3 sm:p-5  md:h-[calc(100vh-100px)]">
      {/* Main Content Grid */}
      <div className="">
        <p className="text-sm sm:text-base text-default-1000 opacity-75">
          Welcome to NxVoy!
        </p>
        <div className="text-2xl sm:text-4xl font-semibold text-subtitle">
          <span className="text-gradient">Meet Shasa</span>, Your Travel
          Companion!
        </div>
      </div>
      {/* Primary Filter Section */}
      <div className="py-3 sm:py-4">
        {componentLoading.filter ? (
          <PrimaryFilterSkeleton />
        ) : (
          <PrimaryFilter />
        )}
      </div>
      <div className="grid grid-cols-1 lg:grid-cols-6 gap-x-3 sm:gap-4 flex-1">
        {/* Chat Section */}
        <div className="lg:col-span-2 order-2 lg:order-1  md:h-[calc(100vh-275px)]  md:overflow-y-scroll">
          {componentLoading.chat ? <ChatSkeleton /> : <ChatWithShasa />}
          <div className="mt-4">
            <p className="text-xl font-bold mb-3">map</p>
            <Image
              src="https://storage.googleapis.com/nxvoytrips-img/nxvoy-travel-agent-holidayPlanner/map.svg"
              alt="map"
              width={200}
              height={300}
              className="w-full h-[350px] object-cover rounded-xl"
            />
          </div>
        </div>

        {/* Recommendations Section */}
        <div className="lg:col-span-4 order-1 lg:order-2  md:h-[calc(100vh-275px)]  md:overflow-y-auto md:overflow-x-hidden">
          {componentLoading.recommendations ? (
            <RecommendationListSkeleton count={5} />
          ) : (
            <Recommendation />
          )}
        </div>
      </div>
    </div>
  );
};

export default ExploreRecommendation;
