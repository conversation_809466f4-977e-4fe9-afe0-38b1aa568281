import { MapPointIcon, CalendarIcon } from '@/components/icons';
import { Card, CardBody, Button } from '@heroui/react';

import Image from 'next/image';

interface Recommendation {
  title: string; // Main recommendation title
  duration: string; // Trip duration (e.g., "4 nights / 5 days")
  location: string; // Destination location
  tags: string; // Comma-separated activity tags
  image: string; // Image URL for the destination
  badge: string; // Status badge (e.g., "Popular", "New")
  imagesm1: string; // Small image 1
  imagesm2: string; // Small image 2
  imagesm3: string; // Small image 3
  imagesm4: string; // Small image 4
}

interface RecommendationCardProps {
  recommendations: Recommendation;
}
const RecommendationCard = ({ recommendations }: RecommendationCardProps) => {
  return (
    <div className="flex flex-col gap-2 flex-1 overflow-hidden mb-3">
      <Card
        key={`${recommendations.title}-${recommendations.location}-${recommendations.duration}`}
        className="bg-white border-none shadow-none hover:shadow-lg hover:border-2 hover:border-black hover:-translate-y-1 hover:!bg-white transition-all duration-300 ease-in-out flex-shrink-0"
        isHoverable
        isPressable
        data-card="recommendation" // Used for DOM measurements
      >
        <CardBody>
          <div className="flex flex-row max-md:flex-col  justify-between rounded-xl cursor-pointer w-full">
            {/* Left section: Image and details */}
            <div className="flex flex-row max-md:flex-col gap-4">
              {/* grid Image  */}

              <div className="flex flex-col gap-1.5 md:w-[230px]">
                {/* Large Image */}
                <div className="w-full">
                  <Image
                    src={recommendations.image}
                    alt="Large Image"
                    width={230}
                    height={120}
                    className="w-full h-[120px] object-cover rounded-tl-xl rounded-tr-xl"
                  />
                </div>

                {/* Small Image Grid */}
                <div className="grid grid-cols-4 gap-1.5">
                  <Image
                    src={recommendations.imagesm1}
                    alt="Small Image 1"
                    width={400}
                    height={200}
                    className="w-full  object-cover rounded-bl-md aspect-square"
                  />
                  <Image
                    src={recommendations.imagesm2}
                    alt="Small Image 2"
                    width={400}
                    height={200}
                    className="w-full aspect-square object-cover "
                  />
                  <Image
                    src={recommendations.imagesm3}
                    alt="Small Image 3"
                    width={400}
                    height={200}
                    className="w-full aspect-square object-cover"
                  />

                  {/* Blurred Last Image with Button */}
                  <div className="relative">
                    <Image
                      src={recommendations.imagesm4}
                      alt="Small Image 4 (Blurred)"
                      width={400}
                      height={200}
                      className="w-full aspect-square object-cover rounded-br-md filter blur-xs "
                    />
                    <div className="absolute inset-0 flex items-center justify-center text-sm text-white">
                      {/* <Button
                        size="sm"
                        className="bg-white text-black text-sm px-4 py-1.5 rounded-full shadow hover:bg-gray-200"
                        variant="flat"
                      > */}
                      View All
                      {/* </Button> */}
                    </div>
                  </div>
                </div>
              </div>
              {/* Text content */}
              <div className="text-sm space-y-0.5">
                <p className="font-medium text-lg">{recommendations.title}</p>
                <div className="flex flex-row gap-3 py-2">
                  <div className="flex flex-row gap-1 items-center">
                    <CalendarIcon
                      size={14}
                      isAnimation={false}
                      className="text-black"
                    />
                    <p className=" text-md">{recommendations.duration}</p>
                  </div>

                  <div className="flex flex-row gap-1 items-center">
                    <MapPointIcon
                      size={14}
                      isAnimation={false}
                      className="text-black"
                    />
                    <p className=" text-md">{recommendations.location}</p>
                  </div>
                </div>
                <div>
                  <p className="text-default-700 text-md">
                    Book with only 30% of total payment
                  </p>
                  <p className="text-default-700 text-md">
                    Customize free of cost
                  </p>
                </div>

                <p className="text-md font-medium py-2">
                  Activities, Explore, Leisure, Family
                </p>

                <p className="text-md text-default-700">
                  Explore Berlin's historic neighborhoods and vibrant street
                  art.
                </p>
              </div>
            </div>
            {/* Right section: Action button */}
            <div className="flex flex-col gap-2 justify-between text-right">
              <p className="text-base font-bold">
                4.2{' '}
                <span className="text-default-700 font-medium">
                  (103 Ratings)
                </span>
              </p>
              <div className="text-right">
                <div className="">
                  <span className="text-default-700 line-through text-md">
                    $3,500
                  </span>
                  <span className="font-bold ml-3 text-xl">$2,999</span>
                </div>
                <div className="flex justify-end py-2">
                  <p className="text-default-700 text-md w-[130px] text-right">
                    $150 taxes & fees Per Person
                  </p>
                </div>
                <Button
                  color="primary"
                  // variant="bordered"
                  size="sm"
                  className="font-semibold  rounded-full bg-primary-200 text-white"
                >
                  Book Trip
                </Button>
              </div>
            </div>
          </div>
        </CardBody>
      </Card>
    </div>
  );
};

export default RecommendationCard;
